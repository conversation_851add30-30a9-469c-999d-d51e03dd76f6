# Read Terminal Tool Implementation Plan

## Overview
This document provides a detailed implementation plan for adding a "Read Terminal" tool to Cline. The goal is to separate terminal command execution from output reading, allowing the AI agent to execute commands and then separately read terminal output at any time, improving flexibility and reducing timeouts/delays.

## Current Terminal Architecture Analysis

### Current Implementation
Based on the codebase analysis, the current terminal system works as follows:

1. **TerminalManager** (`src/integrations/terminal/TerminalManager.ts`):
   - Manages terminal creation, reuse, and command execution
   - Uses VSCode's shell integration API when available
   - Falls back to `sendText()` for older VSCode versions
   - Tracks terminal state (busy/idle, CWD, shell path)

2. **TerminalProcess** (`src/integrations/terminal/TerminalProcess.ts`):
   - Extends EventEmitter and implements Promise interface
   - Handles real-time output streaming via `line` events
   - Manages command completion detection with grace periods
   - Provides `getUnretrievedOutput()` for missed output
   - Uses shell integration's `executeCommand()` API when available

3. **Current Command Execution Flow**:
   - `bashTool` → `ToolExecutor.executeCommandTool()` → `Task.executeCommandTool()` → `TerminalManager.runCommand()`
   - Single tool call handles both execution AND output capture
   - Waits for command completion or user intervention ("Proceed While Running")
   - Can timeout or hang if output detection fails

4. **Output Retrieval Methods**:
   - **Primary**: Shell integration streaming via `execution.read()`
   - **Fallback**: `getLatestTerminalOutput()` using clipboard copy (`src/integrations/terminal/get-latest-output.ts`)
   - **Unretrieved**: `TerminalProcess.getUnretrievedOutput()` for background processes

### Current Issues
1. **Blocking Behavior**: Single tool waits for completion, can hang indefinitely
2. **Output Detection Failures**: Sometimes commands execute successfully but output isn't captured
3. **Inflexibility**: Can't read terminal output at arbitrary times during workflow
4. **Grace Period Complexity**: Complex logic to detect command completion vs long-running processes

## Proposed Solution: Read Terminal Tool

### Core Concept
Split terminal operations into two separate tools:
1. **Execute Command Tool** (existing `bashTool`): Execute commands without waiting for completion
2. **Read Terminal Tool** (new): Read current terminal output on-demand

### Benefits
1. **Non-blocking Execution**: Commands can run in background
2. **Flexible Output Reading**: Read terminal anytime during workflow
3. **Better Error Handling**: Separate concerns of execution vs output reading
4. **Improved Reliability**: Reduce timeouts and hanging issues
5. **Enhanced Workflow Control**: AI can decide when to check output

## Detailed Implementation Plan

### Phase 1: Create Read Terminal Tool Definition

#### 1.1 Create Tool Definition File
**File**: `src/core/tools/readTerminalTool.ts`

```typescript
export const readTerminalToolName = "ReadTerminal"

const descriptionForAgent = `Request to read the current output from a terminal. Use this tool to check the output of previously executed commands or to monitor the status of long-running processes. This tool can be called at any time to get the latest terminal content.

Usage Guidelines:
- Use this after executing commands with the Bash tool to get their output
- Can be called multiple times to monitor long-running processes
- Useful for checking if commands completed successfully
- Returns the current visible terminal content or recent output`

export const readTerminalToolDefinition = () => ({
    name: readTerminalToolName,
    descriptionForAgent,
    inputSchema: {
        type: "object",
        properties: {
            terminal_id: {
                type: "number",
                description: "Optional terminal ID to read from. If not provided, reads from the most recently used terminal.",
            },
            lines: {
                type: "number", 
                description: "Optional number of recent lines to retrieve. Defaults to all available output.",
            }
        },
        required: [],
    },
})
```

#### 1.2 Add Tool to System Prompts
**Files to modify**:
- `src/core/prompts/system.ts` (legacy models)
- `src/core/prompts/model_prompts/claude4.ts` (Claude 4)
- `src/core/prompts/model_prompts/claude4-experimental.ts` (Claude 4 experimental)

**Changes**:
1. Import the new tool definition
2. Add tool to the tools array
3. Update system prompt text to mention the new tool
4. Add usage guidelines in the prompt

### Phase 2: Implement Tool Execution Logic

#### 2.1 Add Case to ToolExecutor
**File**: `src/core/task/ToolExecutor.ts`

Add new case in `executeTool()` method:

```typescript
case "read_terminal": {
    const terminalId: number | undefined = block.params.terminal_id
    const lines: number | undefined = block.params.lines
    
    const sharedMessageProps: ClineSayTool = {
        tool: "readTerminal",
        terminalId: terminalId,
        lines: lines,
    }
    
    try {
        if (block.partial) {
            // Handle partial streaming
            const partialMessage = JSON.stringify({
                ...sharedMessageProps,
                content: "",
            } satisfies ClineSayTool)
            
            if (this.shouldAutoApproveTool(block.name)) {
                this.removeLastPartialMessageIfExistsWithType("ask", "tool")
                await this.say("tool", partialMessage, undefined, undefined, block.partial)
            } else {
                this.removeLastPartialMessageIfExistsWithType("say", "tool")
                await this.ask("tool", partialMessage, block.partial).catch(() => {})
            }
            break
        } else {
            this.taskState.consecutiveMistakeCount = 0
            
            const completeMessage = JSON.stringify({
                ...sharedMessageProps,
                content: "Reading terminal output...",
            } satisfies ClineSayTool)
            
            if (this.shouldAutoApproveTool(block.name)) {
                this.removeLastPartialMessageIfExistsWithType("ask", "tool")
                await this.say("tool", completeMessage, undefined, undefined, false)
                this.taskState.consecutiveAutoApprovedRequestsCount++
                telemetryService.captureToolUsage(this.taskId, block.name, this.api.getModel().id, true, true)
            } else {
                showNotificationForApprovalIfAutoApprovalEnabled(
                    `Cline wants to read terminal output`,
                    this.autoApprovalSettings.enabled,
                    this.autoApprovalSettings.enableNotifications,
                )
                this.removeLastPartialMessageIfExistsWithType("say", "tool")
                const didApprove = await this.askApproval("tool", block, completeMessage)
                if (!didApprove) {
                    telemetryService.captureToolUsage(this.taskId, block.name, this.api.getModel().id, false, false)
                    await this.saveCheckpoint()
                    break
                }
                telemetryService.captureToolUsage(this.taskId, block.name, this.api.getModel().id, false, true)
            }
            
            // Execute the tool
            const result = await this.readTerminalTool(terminalId, lines)
            this.pushToolResult(result, block)
            await this.saveCheckpoint()
            break
        }
    } catch (error) {
        await this.handleError("reading terminal", error, block)
        await this.saveCheckpoint()
        break
    }
}
```

#### 2.2 Add Tool Implementation Method
**File**: `src/core/task/ToolExecutor.ts`

Add new method to ToolExecutor class:

```typescript
private async readTerminalTool(terminalId?: number, lines?: number): Promise<string> {
    // Implementation will call terminalManager methods
    // This will be passed as callback to ToolExecutor constructor
    return await this.readTerminalToolCallback(terminalId, lines)
}
```

#### 2.3 Update ToolExecutor Constructor
**File**: `src/core/task/ToolExecutor.ts`

Add new callback parameter:

```typescript
constructor(
    // ... existing parameters
    private readTerminalToolCallback: (terminalId?: number, lines?: number) => Promise<string>,
) {
    // ... existing constructor logic
}
```

### Phase 3: Implement Terminal Reading Logic

#### 3.1 Add Methods to TerminalManager
**File**: `src/integrations/terminal/TerminalManager.ts`

Add new methods:

```typescript
/**
 * Read output from a specific terminal or the most recent one
 */
async readTerminalOutput(terminalId?: number, lines?: number): Promise<string> {
    let targetTerminal: TerminalInfo | undefined
    
    if (terminalId) {
        targetTerminal = TerminalRegistry.getTerminal(terminalId)
        if (!targetTerminal) {
            throw new Error(`Terminal with ID ${terminalId} not found`)
        }
    } else {
        // Get most recently used terminal
        const terminals = TerminalRegistry.getAllTerminals()
        if (terminals.length === 0) {
            throw new Error("No terminals available")
        }
        targetTerminal = terminals.reduce((latest, current) => 
            current.lastActive > latest.lastActive ? current : latest
        )
    }
    
    // Try to get unretrieved output first (for ongoing processes)
    const process = this.processes.get(targetTerminal.id)
    if (process) {
        const unretrievedOutput = process.getUnretrievedOutput()
        if (unretrievedOutput.trim()) {
            return this.processOutput(this.limitLines(unretrievedOutput, lines))
        }
    }
    
    // Fall back to reading current terminal content
    const terminalContent = await getLatestTerminalOutput()
    return this.processOutput(this.limitLines(terminalContent, lines))
}

/**
 * Limit output to specified number of lines
 */
private limitLines(output: string, lines?: number): string {
    if (!lines) return output
    
    const outputLines = output.split('\n')
    if (outputLines.length <= lines) return output
    
    // Return last N lines
    return outputLines.slice(-lines).join('\n')
}

/**
 * Get list of available terminals with their status
 */
getTerminalList(): Array<{id: number, busy: boolean, lastCommand: string, lastActive: number}> {
    return TerminalRegistry.getAllTerminals().map(t => ({
        id: t.id,
        busy: t.busy,
        lastCommand: t.lastCommand,
        lastActive: t.lastActive
    }))
}
```

#### 3.2 Update Task Class
**File**: `src/core/task/index.ts`

Add new method to Task class:

```typescript
private async readTerminalTool(terminalId?: number, lines?: number): Promise<string> {
    try {
        const output = await this.terminalManager.readTerminalOutput(terminalId, lines)
        
        if (!output.trim()) {
            return "No output available in terminal."
        }
        
        return formatResponse.toolResult(
            `Terminal output:\n\n${output}`,
            undefined, // no images
            undefined  // no file content
        )
    } catch (error) {
        throw new Error(`Failed to read terminal: ${error.message}`)
    }
}
```

#### 3.3 Wire Up the Callback
**File**: `src/core/task/index.ts`

Update ToolExecutor instantiation to include the new callback:

```typescript
this.toolExecutor = new ToolExecutor(
    // ... existing parameters
    this.readTerminalTool.bind(this),
)
```

### Phase 4: Modify Bash Tool Behavior

#### 4.1 Update Bash Tool Description
**File**: `src/core/tools/bashTool.ts`

Update the `descriptionForAgent` to mention the new workflow:

```typescript
const descriptionForAgent = (cwd: string) => `Request to execute a CLI command on the system. Use this when you need to perform system operations or run specific commands to accomplish any step in the user's task.

IMPORTANT: This tool executes commands but does not wait for their completion or capture their output. After executing a command, use the ReadTerminal tool to check the output and verify the command completed successfully.

For long-running processes (like servers), you can execute the command and then use ReadTerminal periodically to monitor the process status.

Usage Pattern:
1. Use this tool to execute a command
2. Use ReadTerminal tool to check the output
3. Repeat ReadTerminal as needed for long-running processes

...rest of existing description...`
```

#### 4.2 Modify Command Execution Flow (Optional)
**File**: `src/core/task/ToolExecutor.ts`

Consider adding a flag to bash tool to control whether it waits for completion:

```typescript
case "execute_command": {
    let command: string | undefined = block.params.command
    const requiresApprovalRaw: string | undefined = block.params.requires_approval
    const waitForCompletion: boolean = block.params.wait_for_completion !== "false" // default true for backward compatibility
    
    // ... existing validation logic ...
    
    if (waitForCompletion) {
        // Existing behavior - wait for completion
        const [userRejected, result] = await this.executeCommandTool(command)
        // ... handle result ...
    } else {
        // New behavior - fire and forget
        const [userRejected, result] = await this.executeCommandToolAsync(command)
        // ... handle result ...
    }
}
```

### Phase 5: Update Auto-Approval Settings

#### 5.1 Add Read Terminal to Auto-Approval
**File**: `src/shared/AutoApprovalSettings.ts`

Add the new tool to auto-approval configuration if it exists.

#### 5.2 Update Auto-Approval Logic
**File**: `src/core/task/tools/autoApprove.ts`

Add logic for auto-approving the read terminal tool:

```typescript
shouldAutoApproveTool(toolName: ToolUseName): boolean | [boolean, boolean] {
    // ... existing logic ...
    
    case "read_terminal":
        // Reading terminal is generally safe, auto-approve if enabled
        return this.settings.enabled
        
    // ... rest of cases ...
}
```

### Phase 6: Update System Prompts and Documentation

#### 6.1 Update System Prompts
**Files**: All system prompt files

Add guidance on using the new tool:

```
- You can use the ReadTerminal tool to check the output of previously executed commands. This is particularly useful after running commands with the Bash tool, as the Bash tool executes commands but doesn't wait for their completion.
- Use ReadTerminal to monitor long-running processes like development servers, build processes, or installations.
- You can call ReadTerminal multiple times to track the progress of ongoing operations.
- The ReadTerminal tool is safe and can be called frequently without side effects.
```

#### 6.2 Update Tool Descriptions
Add the new tool to the tool descriptions section in system prompts:

```
ReadTerminal: Read the current output from a terminal. Use this to check command results, monitor long-running processes, or verify command completion. Can specify terminal ID and number of lines to retrieve.
```

### Phase 7: Error Handling and Edge Cases

#### 7.1 Handle Missing Terminals
- Graceful error when no terminals exist
- Clear error messages for invalid terminal IDs
- Fallback to creating new terminal if needed

#### 7.2 Handle Empty Output
- Return meaningful message when no output available
- Distinguish between "no output yet" vs "command completed with no output"

#### 7.3 Handle Large Output
- Implement line limiting to prevent overwhelming the AI
- Add truncation indicators when output is cut off
- Consider adding pagination for very large outputs

### Phase 8: Testing and Validation

#### 8.1 Unit Tests
Create tests for:
- `readTerminalTool` method
- Terminal output reading logic
- Line limiting functionality
- Error handling scenarios

#### 8.2 Integration Tests
Test scenarios:
- Execute command → Read terminal workflow
- Multiple terminal management
- Long-running process monitoring
- Error conditions

#### 8.3 Manual Testing
Test with various command types:
- Quick commands (ls, pwd, echo)
- Long-running processes (npm run dev, python server)
- Commands with no output
- Commands with large output
- Error-producing commands

## Implementation Considerations

### Backward Compatibility
- Keep existing bash tool behavior as default
- Add optional parameters for new behavior
- Ensure existing workflows continue to work

### Performance
- Avoid excessive terminal reading
- Cache recent output when appropriate
- Limit output size to prevent memory issues

### User Experience
- Clear tool descriptions for AI understanding
- Meaningful error messages
- Consistent behavior across different terminal types

### Security
- Validate terminal IDs to prevent access to unauthorized terminals
- Ensure read-only access (no command injection)
- Respect existing permission models

## Migration Strategy

### Phase 1: Implement Core Functionality
- Add read terminal tool
- Basic terminal reading capability
- Integration with existing system

### Phase 2: Update AI Prompts
- Modify system prompts to guide AI usage
- Update tool descriptions
- Add usage examples

### Phase 3: Optimize Bash Tool
- Add non-blocking execution option
- Update tool description
- Maintain backward compatibility

### Phase 4: Advanced Features
- Terminal selection improvements
- Output filtering and formatting
- Performance optimizations

## Success Metrics

1. **Reduced Timeouts**: Fewer instances of bash tool hanging or timing out
2. **Improved Flexibility**: AI can check terminal output at appropriate times
3. **Better Error Handling**: Clearer distinction between execution and output issues
4. **Enhanced Workflows**: More sophisticated command execution patterns
5. **User Satisfaction**: Fewer manual interventions required

## Conclusion

This implementation plan provides a comprehensive approach to adding a Read Terminal tool to Cline. The separation of command execution and output reading will significantly improve the flexibility and reliability of terminal operations, while maintaining backward compatibility with existing workflows.

The phased approach allows for incremental implementation and testing, ensuring stability throughout the development process.