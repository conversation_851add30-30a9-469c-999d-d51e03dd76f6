export const readTerminalToolName = "ReadTerminal"

const descriptionForAgent = `Request to read the current output from a terminal. Use this tool to check the output of previously executed commands or to monitor the status of long-running processes. This tool can be called at any time to get the latest terminal content.

Usage Guidelines:
- Use this after executing commands with the Bash tool to get their output
- Can be called multiple times to monitor long-running processes
- Useful for checking if commands completed successfully
- Returns the current visible terminal content or recent output
- This is a read-only operation that doesn't affect terminal state

Examples:
- After running "npm install", use this to check if installation completed
- Monitor build processes by calling this periodically
- Check error messages from failed commands
- Verify server startup status for long-running processes`

export const readTerminalToolDefinition = () => ({
	name: readTerminalToolName,
	descriptionForAgent,
	inputSchema: {
		type: "object",
		properties: {
			terminal_id: {
				type: "number",
				description: "Optional terminal ID to read from. If not provided, reads from the most recently used terminal.",
			},
			lines: {
				type: "number",
				description: "Optional number of recent lines to retrieve. Defaults to all available output. Use this to limit large outputs.",
			},
		},
		required: [],
	},
})