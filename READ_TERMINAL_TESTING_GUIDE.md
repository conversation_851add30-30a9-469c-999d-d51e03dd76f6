# Read Terminal Tool Testing Guide

## Pre-Testing Checklist

### 1. Code Compilation Test
First, verify the code compiles without errors:

```bash
# In the project root directory
npm run check-types
# This runs: npm run protos && tsc --noEmit

# Alternative individual commands:
npm run compile
# This runs: npm run check-types && npm run lint && node esbuild.js

# For full build:
npm run package
# This runs: npm run check-types && npm run build:webview && npm run lint && node esbuild.js --production
```

If there are TypeScript errors, they need to be fixed before proceeding.

### 2. Extension Loading Test
1. Open VS Code
2. Press `F5` or go to Run > Start Debugging
3. This should open a new VS Code window with the extension loaded
4. Check the VS Code Developer Console (Help > Toggle Developer Tools) for any loading errors

## Testing Scenarios

### Test 1: Basic Functionality Test

**Objective**: Verify the read_terminal tool appears and can be called

**Steps**:
1. Open Cline in the extension development window
2. Start a new task
3. Ask Cline: "Can you show me what tools you have available?"
4. Look for `read_terminal` in the response
5. Try a simple command: "Use the read_terminal tool to check if there's any terminal output"

**Expected Result**: 
- Tool should be recognized
- Should either show "No terminals available" or show some terminal content

### Test 2: Command Execution + Read Terminal Workflow

**Objective**: Test the new workflow pattern

**Steps**:
1. Ask Cline: "Execute the command 'echo Hello World' and then read the terminal output"
2. Observe if Cline:
   - First uses `execute_command` 
   - Then uses `read_terminal` to check the output
3. Verify the output shows "Hello World"

**Expected Result**:
- Two separate tool calls should be made
- Terminal output should be captured and displayed

### Test 3: Long-Running Process Monitoring

**Objective**: Test monitoring of ongoing processes

**Steps**:
1. Ask Cline: "Start a simple HTTP server using Python and then check if it's running"
2. Command should be something like: `python -m http.server 8000`
3. Cline should then use `read_terminal` to verify the server started
4. Ask Cline to "Check the terminal again to see the server status"

**Expected Result**:
- Server should start
- Multiple `read_terminal` calls should show server status
- Should see "Serving HTTP on..." message

### Test 4: Terminal ID Specification

**Objective**: Test reading from specific terminals

**Steps**:
1. Have multiple commands running in different terminals
2. Ask Cline to "Read output from terminal ID 1" (or whatever ID exists)
3. Verify it reads from the correct terminal

**Expected Result**:
- Should read from specified terminal
- Should show appropriate error if terminal ID doesn't exist

### Test 5: Line Limiting

**Objective**: Test the lines parameter

**Steps**:
1. Generate a lot of output: Ask Cline to "Run 'ls -la /' and then read only the last 5 lines"
2. Verify only 5 lines are returned

**Expected Result**:
- Should limit output to specified number of lines
- Should show the most recent lines

### Test 6: Error Handling

**Objective**: Test error scenarios

**Steps**:
1. Try reading from non-existent terminal: "Read from terminal ID 999"
2. Try reading when no terminals exist (fresh VS Code instance)

**Expected Result**:
- Should show appropriate error messages
- Should not crash the extension

### Test 7: Auto-Approval

**Objective**: Test auto-approval functionality

**Steps**:
1. Enable auto-approval for read operations in Cline settings
2. Use `read_terminal` tool
3. Verify it doesn't ask for approval

**Expected Result**:
- Should auto-approve since it's a read-only operation
- Should execute without user confirmation

## Debugging Steps

### If the tool doesn't appear:

1. **Check TypeScript compilation**:
   ```bash
   npm run build
   ```

2. **Check extension console**:
   - Open VS Code Developer Tools
   - Look for errors in Console tab

3. **Verify imports**:
   - Check that all imports in the modified files are correct
   - Ensure the tool is properly exported

### If tool appears but doesn't work:

1. **Check the callback wiring**:
   - Verify `readTerminalTool` callback is properly passed to ToolExecutor
   - Check that the method exists in Task class

2. **Check terminal manager**:
   - Verify `readTerminalOutput` method exists in TerminalManager
   - Check for any runtime errors in the method

3. **Debug with console logs**:
   Add temporary console.log statements:
   ```typescript
   // In ToolExecutor.ts
   case "read_terminal": {
       console.log("Read terminal tool called with:", block.params);
       // ... rest of code
   }
   
   // In TerminalManager.ts
   async readTerminalOutput(terminalId?: number, lines?: number): Promise<string> {
       console.log("Reading terminal output:", { terminalId, lines });
       // ... rest of code
   }
   ```

## Manual Testing Commands

### Quick Test Commands:
```bash
# Simple output
echo "Test message"

# Multi-line output  
ls -la

# Long-running process
python -m http.server 8000

# Process with continuous output
ping google.com

# Command that produces no output
mkdir test_dir
```

### Test Scenarios to Try:

1. **Basic workflow**:
   - "Execute 'pwd' and then read the terminal output"

2. **Multiple commands**:
   - "Execute 'echo first' then 'echo second' then read the terminal"

3. **Long output**:
   - "Execute 'find / -name '*.txt' 2>/dev/null | head -100' and read the last 10 lines"

4. **Server monitoring**:
   - "Start a simple web server and monitor its status"

## Expected Behavior

### Successful Implementation Should Show:

1. **Tool Recognition**: Cline recognizes and can use `read_terminal`
2. **Workflow Separation**: Commands and output reading are separate operations
3. **Flexible Timing**: Can read terminal output at any time
4. **Error Handling**: Graceful handling of missing terminals
5. **Output Control**: Can limit output with `lines` parameter
6. **Auto-Approval**: Works with auto-approval settings

### Common Issues and Solutions:

| Issue | Likely Cause | Solution |
|-------|--------------|----------|
| Tool not recognized | Import/export issue | Check tool registration in prompts |
| "No terminals available" | Terminal manager issue | Check terminal creation logic |
| Empty output | Terminal reading issue | Check `getLatestTerminalOutput` function |
| TypeScript errors | Type mismatch | Check parameter types in interfaces |
| Extension crash | Runtime error | Check console for error details |

## Performance Testing

### Test with various scenarios:
1. **Small output** (< 100 lines)
2. **Medium output** (100-1000 lines)  
3. **Large output** (> 1000 lines)
4. **Multiple terminals** (3-5 active terminals)
5. **Rapid calls** (multiple read_terminal calls in succession)

## Integration Testing

### Test with existing Cline features:
1. **With auto-approval enabled/disabled**
2. **With different terminal profiles**
3. **With MCP servers running**
4. **With browser actions**
5. **With file operations**

## Success Criteria

✅ **Implementation is successful if**:
- Tool compiles without errors
- Tool appears in Cline's available tools
- Can execute commands and read output separately
- Handles errors gracefully
- Works with auto-approval
- Improves workflow flexibility
- Doesn't break existing functionality

## Reporting Issues

If you find issues, please note:
1. **Error messages** (exact text)
2. **Steps to reproduce**
3. **Expected vs actual behavior**
4. **Console logs** (if any)
5. **VS Code version**
6. **Operating system**

This will help identify and fix any implementation issues quickly.