# Quick Testing Steps for Read Terminal Tool

## ⚡ Immediate Testing Commands

Since there are system compatibility issues preventing direct build testing, here are the correct commands for you to run:

### 1. **Type Check (Most Important)**
```bash
npm run check-types
```
This will:
- Generate protobuf files
- Run TypeScript compilation without emitting files
- Catch any type errors in our implementation

### 2. **Full Compilation**
```bash
npm run compile
```
This will:
- Run type checking
- Run linting
- Build the extension with esbuild

### 3. **Complete Build**
```bash
npm run package
```
This will:
- Build everything including the webview
- Create production-ready extension

## 🔍 What to Look For

### ✅ **Success Indicators**
- No TypeScript errors
- No ESLint errors
- Build completes successfully
- Extension loads in VS Code debug mode

### ❌ **Common Issues to Fix**

1. **Import Errors**
   ```
   Cannot find module '@core/tools/readTerminalTool'
   ```
   **Fix**: Check import paths in the prompt files

2. **Type Errors**
   ```
   Property 'readTerminalTool' does not exist on type...
   ```
   **Fix**: Check callback parameter in ToolExecutor constructor

3. **Missing Tool Registration**
   ```
   Tool 'read_terminal' not recognized
   ```
   **Fix**: Ensure tool is added to toolUseNames array

## 🚀 VS Code Extension Testing

### Step 1: Build and Load
```bash
# Build the extension
npm run compile

# Open VS Code for extension development
code .

# Press F5 to start debugging (opens new VS Code window with extension)
```

### Step 2: Quick Functionality Test
In the new VS Code window:

1. **Open Cline** (click the robot icon in sidebar)
2. **Start a new task**
3. **Test basic command**: 
   ```
   "Execute 'echo Hello World' and then read the terminal output"
   ```

### Step 3: Expected Behavior
You should see:
1. **First tool call**: `execute_command` with "echo Hello World"
2. **Second tool call**: `read_terminal` (no parameters)
3. **Output**: Should show "Hello World"

## 🐛 Debugging Steps

### If Build Fails:
1. **Check the error message carefully**
2. **Look for specific file/line references**
3. **Common fixes**:
   - Missing imports
   - Type mismatches
   - Syntax errors

### If Extension Doesn't Load:
1. **Check VS Code Developer Console**:
   - Help > Toggle Developer Tools
   - Look for red errors in Console tab
2. **Check Extension Host Log**:
   - View > Output > Select "Extension Host" from dropdown

### If Tool Doesn't Work:
1. **Check if tool appears in system prompt**:
   Ask Cline: "What tools do you have available?"
   Look for "read_terminal" in the response

2. **Test with simple command first**:
   ```
   "Use the read_terminal tool"
   ```

## 📋 Minimal Test Script

Create this simple test to verify everything works:

```bash
# 1. Build
npm run compile

# 2. Start VS Code debug
# Press F5 in VS Code

# 3. In the debug window, open Cline and try:
# "Execute 'pwd' then read the terminal"

# 4. Expected: Two separate tool calls, output shows current directory
```

## 🔧 Quick Fixes for Common Issues

### Issue: TypeScript errors about missing properties
**Fix**: Check that all new parameters are added to interfaces:
- `terminal_id` and `lines` in `toolParamNames`
- `read_terminal` in `toolUseNames`
- Properties in `ClineSayTool` interface

### Issue: Tool not recognized
**Fix**: Verify tool is imported and added to tools array in:
- `src/core/prompts/model_prompts/claude4-experimental.ts`

### Issue: Callback not found
**Fix**: Check that `readTerminalTool` method exists in Task class and is passed to ToolExecutor

### Issue: Terminal reading fails
**Fix**: Verify `readTerminalOutput` method exists in TerminalManager

## 🎯 Success Criteria

The implementation is working if:
- ✅ `npm run compile` succeeds
- ✅ Extension loads without errors
- ✅ Tool appears in Cline's available tools
- ✅ Can execute command + read terminal in sequence
- ✅ Shows appropriate output or error messages

## 📞 Next Steps

1. **Run `npm run compile`** first
2. **Fix any TypeScript/build errors**
3. **Test in VS Code debug mode**
4. **Try the basic workflow test**
5. **Report any issues with specific error messages**

The implementation should work correctly if the build succeeds!